-- 创建升贴水计算规则表
CREATE TABLE `sd_premium_discount_rule`
(
    `id`         INT          NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `rule_name`  VARCHAR(255) NOT NULL COMMENT '规则名称',
    `is_enabled` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用(1=启用, 0=禁用)',
    `created_at` TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_rule_name` (`rule_name`) COMMENT '规则名称唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='升贴水计算规则表';

-- 创建升贴水规则详情表
CREATE TABLE `sd_premium_discount_rule_detail`
(
    `id`              INT            NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `rule_id`         INT            NOT NULL COMMENT '关联规则ID',
    `condition_field` VARCHAR(50)    NOT NULL COMMENT '条件字段(如weight, region)',
    `operator`        ENUM('>', '<', '=', '>=', '<=', '!=') NOT NULL COMMENT '条件操作符',
    `condition_value` VARCHAR(255)   NOT NULL COMMENT '条件值(灵活存储)',
    `discount_value`  DECIMAL(10, 2) NOT NULL COMMENT '升贴水值',
    `created_at`      TIMESTAMP      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      TIMESTAMP      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    PRIMARY KEY (`id`),
    KEY               `idx_rule_id` (`rule_id`) COMMENT '规则ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='升贴水规则详情表';
