<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <include file="Public:header" />
</head>
<body>
    <div class="x-body">
        <button id="add_click" class="layui-btn"><i class="layui-icon"></i>添加条件字段</button>
        
        <script>
            $('#add_click').click(function(){
                var aa = "{:U('add')}";
                x_admin_show('添加条件字段', aa);
            });
        </script>

        <table class="tbl2 bg_f w_100 rad_10 mt_10 clr_79">
            <thead>
                <tr>
                    <th class="al_lt">字段代码</th>
                    <th class="al_lt">字段名称</th>
                    <th class="al_lt">字段类型</th>
                    <th class="al_lt">单位</th>
                    <th class="al_lt">支持范围</th>
                    <th class="al_lt">选择项</th>
                    <th class="al_lt">状态</th>
                    <th class="al_lt">排序</th>
                    <th class="al_lt">操作</th>
                </tr>
            </thead>
            <tbody>
                <foreach name="fields" item="v">
                    <tr>
                        <td>
                            <code>{$v.field_code}</code>
                            （ID：{$v.id}）
                        </td>
                        <td>
                            <a href="javascript:;" my_url="{:U('edit',array('id'=>$v['id']))}" class="edit_item" title="编辑" style="font-size:14px;">
                                {$v.field_name}
                            </a>
                        </td>
                        <td>
                            <span class="layui-badge <if condition='$v.field_type eq \"number\"'>layui-bg-blue<elseif condition='$v.field_type eq \"select\"' />layui-bg-orange<else />layui-bg-gray</if>">
                                {$field_types[$v['field_type']]}
                            </span>
                        </td>
                        <td>
                            <if condition="$v['field_unit']">
                                {$v.field_unit}
                            <else />
                                -
                            </if>
                        </td>
                        <td>
                            <if condition="$v['is_range'] eq 1">
                                <span class="layui-badge layui-bg-green">支持</span>
                            <else />
                                <span class="layui-badge">不支持</span>
                            </if>
                        </td>
                        <td style="max-width: 200px; word-break: break-all;">
                            {$v.select_options_display}
                        </td>
                        <td>
                            <if condition="$v['is_enabled'] eq 1">
                                <span class="layui-badge layui-bg-green">启用</span>
                            <else />
                                <span class="layui-badge">禁用</span>
                            </if>
                        </td>
                        <td>
                            <input class="layui-input sort_blur" myid="{$v.id}" value="{$v.sort_order}" style="width:60px;" />
                        </td>
                        <td>
                            <a href="javascript:;" my_url="{:U('edit',array('id'=>$v['id']))}" class="edit_item layui-btn layui-btn-xs" title="编辑">编辑</a>
                            <a href="javascript:;" my_url="{:U('toggleStatus', array('id'=>$v['id']))}" class="toggle_item layui-btn layui-btn-xs <if condition='$v.is_enabled eq 1'>layui-btn-warm<else />layui-btn-normal</if>" title="切换状态">
                                <if condition="$v['is_enabled'] eq 1">禁用<else />启用</if>
                            </a>
                            <a href="javascript:;" my_url="{:U('del', array('id'=>$v['id']))}" class="del_item layui-btn layui-btn-xs layui-btn-danger" title="删除">删除</a>
                        </td>
                    </tr>
                </foreach>
            </tbody>
        </table>

        <script>
            $('.edit_item').click(function(){
                x_admin_show('编辑条件字段', $(this).attr('my_url'));
            });
            
            $('.toggle_item').click(function(){
                var aa = $(this);
                layer.confirm('确认切换状态？', {icon: 3, title:'提示'}, function(index){
                    $.getJSON(aa.attr('my_url'), function(d){
                        if(d.code==1){
                            layer.msg(d.msg, {icon: 1});
                            setTimeout(function(){
                                history.go(0);
                            }, 1000);
                        }else{
                            layer.msg(d.msg, {icon: 2});
                        }
                    });
                    layer.close(index);
                });
            });
            
            $('.del_item').click(function(){
                var aa = $(this);
                layer.confirm('确认删除？删除后不可恢复！', {icon: 3, title:'提示'}, function(index){
                    $.getJSON(aa.attr('my_url'), function(d){
                        if(d.code==1){
                            layer.msg(d.msg, {icon: 1});
                            setTimeout(function(){
                                history.go(0);
                            }, 1000);
                        }else{
                            layer.msg(d.msg, {icon: 2});
                        }
                    });
                    layer.close(index);
                });
            });
            
            $('.sort_blur').blur(function(){
                if($(this).val()!=''){
                    $.getJSON('{:U("updateSort")}', {id:$(this).attr('myid'), sort_order:$(this).val()}, function(d){
                        if(d.code==1){
                            layer.msg(d.msg, {icon: 1});
                        }
                    });
                }
            });
        </script>
    </div>
</body>
</html>
