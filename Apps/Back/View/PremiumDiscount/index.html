<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <include file="Public:header" />
</head>
<body>
    <div class="x-body">
        <button id="add_click" class="layui-btn"><i class="layui-icon"></i>添加规则</button>
        
        <script>
            $('#add_click').click(function(){
                var aa = "{:U('add')}";
                x_admin_show('添加升贴水规则', aa);
            });
        </script>

        <table class="tbl2 bg_f w_100 rad_10 mt_10 clr_79">
            <thead>
                <tr>
                    <th class="al_lt">规则名称</th>
                    <th class="al_lt">状态</th>
                    <th class="al_lt">详情数量</th>
                    <th class="al_lt">创建时间</th>
                    <th class="al_lt">更新时间</th>
                    <th class="al_lt">操作</th>
                </tr>
            </thead>
            <tbody>
                <foreach name="rules" item="v">
                    <tr>
                        <td>
                            <a href="javascript:;" my_url="{:U('edit',array('id'=>$v['id']))}" class="edit_item" title="编辑" style="font-size:14px;">
                                {$v.rule_name}
                            </a>
                            （ID：{$v.id}）
                        </td>
                        <td>
                            <if condition="$v['is_enabled'] eq 1">
                                <span class="layui-badge layui-bg-green">启用</span>
                            <else />
                                <span class="layui-badge">禁用</span>
                            </if>
                        </td>
                        <td>
                            <a href="{:U('detail', array('rule_id'=>$v['id']))}" class="layui-btn layui-btn-xs">
                                {$v.detail_count} 条详情
                            </a>
                        </td>
                        <td>{$v.created_at}</td>
                        <td>{$v.updated_at}</td>
                        <td>
                            <a href="javascript:;" my_url="{:U('detail',array('rule_id'=>$v['id']))}" class="detail_item layui-btn layui-btn-xs" title="管理详情">详情</a>
                            <a href="javascript:;" my_url="{:U('edit',array('id'=>$v['id']))}" class="edit_item layui-btn layui-btn-xs" title="编辑">编辑</a>
                            <a href="javascript:;" my_url="{:U('toggleStatus', array('id'=>$v['id']))}" class="toggle_item layui-btn layui-btn-xs <if condition='$v.is_enabled eq 1'>layui-btn-warm<else />layui-btn-normal</if>" title="切换状态">
                                <if condition="$v['is_enabled'] eq 1">禁用<else />启用</if>
                            </a>
                            <a href="javascript:;" my_url="{:U('del', array('id'=>$v['id']))}" class="del_item layui-btn layui-btn-xs layui-btn-danger" title="删除">删除</a>
                        </td>
                    </tr>
                </foreach>
            </tbody>
        </table>

        <script>
            $('.detail_item').click(function(){
                window.location.href = $(this).attr('my_url');
            });
            
            $('.edit_item').click(function(){
                x_admin_show('编辑规则', $(this).attr('my_url'));
            });
            
            $('.toggle_item').click(function(){
                var aa = $(this);
                layer.confirm('确认切换状态？', {icon: 3, title:'提示'}, function(index){
                    $.getJSON(aa.attr('my_url'), function(d){
                        if(d.code==1){
                            layer.msg(d.msg, {icon: 1});
                            setTimeout(function(){
                                history.go(0);
                            }, 1000);
                        }else{
                            layer.msg(d.msg, {icon: 2});
                        }
                    });
                    layer.close(index);
                });
            });
            
            $('.del_item').click(function(){
                var aa = $(this);
                layer.confirm('确认删除？删除后不可恢复！', {icon: 3, title:'提示'}, function(index){
                    $.getJSON(aa.attr('my_url'), function(d){
                        if(d.code==1){
                            layer.msg(d.msg, {icon: 1});
                            setTimeout(function(){
                                history.go(0);
                            }, 1000);
                        }else{
                            layer.msg(d.msg, {icon: 2});
                        }
                    });
                    layer.close(index);
                });
            });
        </script>
    </div>
</body>
</html>
