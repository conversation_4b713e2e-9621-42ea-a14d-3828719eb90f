<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <include file="Public:header" />
</head>
<body>
    <div class="x-body">
        <div class="layui-card">
            <div class="layui-card-header">
                <h4>添加规则详情 - {$rule.rule_name}</h4>
            </div>
        </div>

        <form class="layui-form" id="form1">
            <input type="hidden" name="rule_id" value="{$rule.id}">

            <div class="layui-form-item">
                <label for="field_id" class="layui-form-label">
                    <span class="x-red">*</span>条件字段
                </label>
                <div class="layui-input-inline">
                    <select name="field_id" lay-verify="required" lay-filter="field_id">
                        <option value="">请选择条件字段</option>
                        <foreach name="condition_fields" item="v">
                            <option value="{$v.id}" data-type="{$v.field_type}" data-range="{$v.is_range}" data-options="{$v.select_options}">{$v.field_name}<if condition="$v['field_unit']">({$v.field_unit})</if></option>
                        </foreach>
                    </select>
                </div>
                <div class="layui-form-mid layui-word-aux">
                    选择要设置条件的字段
                </div>
            </div>

            <div class="layui-form-item">
                <label for="condition_type" class="layui-form-label">
                    <span class="x-red">*</span>条件类型
                </label>
                <div class="layui-input-inline">
                    <select name="condition_type" lay-verify="required" lay-filter="condition_type">
                        <option value="">请选择条件类型</option>
                        <foreach name="condition_types" item="v" key="k">
                            <option value="{$k}">{$v}</option>
                        </foreach>
                    </select>
                </div>
                <div class="layui-form-mid layui-word-aux">
                    单值条件或范围条件
                </div>
            </div>

            <div class="layui-form-item">
                <label for="operator" class="layui-form-label">
                    <span class="x-red">*</span>操作符
                </label>
                <div class="layui-input-inline">
                    <select name="operator" lay-verify="required">
                        <option value="">请选择操作符</option>
                        <foreach name="operators" item="v" key="k">
                            <option value="{$k}">{$v}</option>
                        </foreach>
                    </select>
                </div>
                <div class="layui-form-mid layui-word-aux">
                    选择比较操作符
                </div>
            </div>

            <div class="layui-form-item" id="single_value_div">
                <label for="condition_value" class="layui-form-label">
                    <span class="x-red">*</span>条件值
                </label>
                <div class="layui-input-inline">
                    <input type="text" id="condition_value" name="condition_value"
                           autocomplete="off" class="layui-input" placeholder="请输入条件值">
                </div>
                <div class="layui-form-mid layui-word-aux">
                    <span class="x-red">*</span>用于比较的值
                </div>
            </div>

            <div class="layui-form-item" id="range_value_div" style="display:none;">
                <label class="layui-form-label">
                    <span class="x-red">*</span>范围值
                </label>
                <div class="layui-input-inline" style="width: 100px;">
                    <input type="text" name="min_value" placeholder="最小值" class="layui-input">
                </div>
                <div class="layui-form-mid">~</div>
                <div class="layui-input-inline" style="width: 100px;">
                    <input type="text" name="max_value" placeholder="最大值" class="layui-input">
                </div>
                <div class="layui-form-mid layui-word-aux">
                    <span class="x-red">*</span>设置范围的最小值和最大值
                </div>
            </div>

            <div class="layui-form-item">
                <label for="discount_value" class="layui-form-label">
                    <span class="x-red">*</span>升贴水值
                </label>
                <div class="layui-input-inline">
                    <input type="number" step="0.01" id="discount_value" name="discount_value" required="" lay-verify="required"
                           autocomplete="off" class="layui-input" placeholder="请输入升贴水值">
                </div>
                <div class="layui-form-mid layui-word-aux">
                    <span class="x-red">*</span>正数为升水，负数为贴水
                </div>
            </div>

            <div class="layui-form-item">
                <label for="" class="layui-form-label"></label>
                <button class="layui-btn" lay-filter="add" lay-submit="">增加</button>
            </div>
        </form>
    </div>

    <script>
        layui.use(['form','layer'], function(){
            $ = layui.jquery;
            var form = layui.form,
                layer = layui.layer;

            //监听字段选择
            form.on('select(field_id)', function(data){
                var $option = $(data.elem).find('option:selected');
                var fieldType = $option.data('type');
                var isRange = $option.data('range');

                // 根据字段类型和是否支持范围来显示条件类型选项
                var $conditionType = $('select[name="condition_type"]');
                $conditionType.empty();
                $conditionType.append('<option value="">请选择条件类型</option>');
                $conditionType.append('<option value="single">单值条件</option>');

                if(isRange == 1) {
                    $conditionType.append('<option value="range">范围条件</option>');
                }

                form.render('select');
            });

            //监听条件类型选择
            form.on('select(condition_type)', function(data){
                if(data.value == 'range') {
                    $('#single_value_div').hide();
                    $('#range_value_div').show();
                } else {
                    $('#single_value_div').show();
                    $('#range_value_div').hide();
                }
            });

            //自定义验证规则
            form.verify({
                condition_value: function(value){
                    var conditionType = $('select[name="condition_type"]').val();
                    if(conditionType == 'single' && value.length < 1){
                        return '条件值不能为空';
                    }
                },
                discount_value: function(value){
                    if(isNaN(value)){
                        return '升贴水值必须是数字';
                    }
                }
            });

            //监听提交
            form.on('submit(add)', function(data){
                console.log(data);
                //发异步，把数据提交给php
                $.ajax({
                    url: "{:U('runAddDetail')}",
                    type: 'POST',
                    data: data.field,
                    dataType: 'json',
                    success: function(res){
                        if(res.code == 1){
                            layer.alert("增加成功", {icon: 1}, function(){
                                // 获得frame索引
                                var index = parent.layer.getFrameIndex(window.name);
                                //关闭当前frame
                                parent.layer.close(index);
                                // 刷新父页面
                                parent.location.reload();
                            });
                        }else{
                            layer.alert(res.msg, {icon: 2});
                        }
                    },
                    error: function(){
                        layer.alert("请求失败", {icon: 2});
                    }
                });
                return false;
            });
        });
    </script>
</body>
</html>
