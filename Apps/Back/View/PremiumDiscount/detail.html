<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <include file="Public:header" />
</head>
<body>
    <div class="x-body">
        <div class="layui-card">
            <div class="layui-card-header">
                <h3>规则详情管理 - {$rule.rule_name}</h3>
                <p>规则ID：{$rule.id} | 状态：
                    <if condition="$rule['is_enabled'] eq 1">
                        <span class="layui-badge layui-bg-green">启用</span>
                    <else />
                        <span class="layui-badge">禁用</span>
                    </if>
                </p>
            </div>
        </div>
        
        <button id="add_detail_click" class="layui-btn"><i class="layui-icon"></i>添加详情</button>
        <a href="{:U('index')}" class="layui-btn layui-btn-primary">返回规则列表</a>
        
        <script>
            $('#add_detail_click').click(function(){
                var aa = "{:U('addDetail', array('rule_id'=>$rule['id']))}";
                x_admin_show('添加规则详情', aa);
            });
        </script>

        <table class="tbl2 bg_f w_100 rad_10 mt_10 clr_79">
            <thead>
                <tr>
                    <th class="al_lt">条件字段</th>
                    <th class="al_lt">条件类型</th>
                    <th class="al_lt">操作符</th>
                    <th class="al_lt">条件值</th>
                    <th class="al_lt">升贴水值</th>
                    <th class="al_lt">创建时间</th>
                    <th class="al_lt">操作</th>
                </tr>
            </thead>
            <tbody>
                <if condition="empty($details)">
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 20px;">
                            暂无规则详情，请添加详情配置
                        </td>
                    </tr>
                <else />
                    <foreach name="details" item="v">
                        <tr>
                            <td>
                                {$v.field_name}
                                <span style="color: #999;">({$v.field_code})</span>
                                <if condition="$v['field_unit']">
                                    <br><small style="color: #999;">单位：{$v.field_unit}</small>
                                </if>
                            </td>
                            <td>
                                <span class="layui-badge <if condition='$v.condition_type eq \"range\"'>layui-bg-blue<else />layui-bg-gray</if>">
                                    {$condition_types[$v['condition_type']]}
                                </span>
                            </td>
                            <td>{$operators[$v['operator']]}</td>
                            <td>
                                {$v.condition_display}
                            </td>
                            <td>
                                <if condition="$v['discount_value'] gt 0">
                                    <span style="color: #5FB878;">+{$v.discount_value}</span>
                                <elseif condition="$v['discount_value'] lt 0" />
                                    <span style="color: #FF5722;">{$v.discount_value}</span>
                                <else />
                                    <span>{$v.discount_value}</span>
                                </if>
                            </td>
                            <td>{$v.created_at}</td>
                            <td>
                                <a href="javascript:;" my_url="{:U('editDetail',array('id'=>$v['id']))}" class="edit_detail_item layui-btn layui-btn-xs" title="编辑">编辑</a>
                                <a href="javascript:;" my_url="{:U('delDetail', array('id'=>$v['id']))}" class="del_detail_item layui-btn layui-btn-xs layui-btn-danger" title="删除">删除</a>
                            </td>
                        </tr>
                    </foreach>
                </if>
            </tbody>
        </table>

        <script>
            $('.edit_detail_item').click(function(){
                x_admin_show('编辑规则详情', $(this).attr('my_url'));
            });
            
            $('.del_detail_item').click(function(){
                var aa = $(this);
                layer.confirm('确认删除？删除后不可恢复！', {icon: 3, title:'提示'}, function(index){
                    $.getJSON(aa.attr('my_url'), function(d){
                        if(d.code==1){
                            layer.msg(d.msg, {icon: 1});
                            setTimeout(function(){
                                history.go(0);
                            }, 1000);
                        }else{
                            layer.msg(d.msg, {icon: 2});
                        }
                    });
                    layer.close(index);
                });
            });
        </script>
    </div>
</body>
</html>
