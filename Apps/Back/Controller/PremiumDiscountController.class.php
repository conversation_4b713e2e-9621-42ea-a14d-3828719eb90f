<?php
namespace Back\Controller;
use Think\Controller;

class PremiumDiscountController extends CommController {
    
    function _initialize(){
        parent::_initialize();
        // 定义条件字段选项
        $this->condition_fields = array(
            'weight' => '重量',
            'region' => '地区',
            'grade' => '等级',
            'moisture' => '水分',
            'impurity' => '杂质'
        );
        
        // 定义操作符选项
        $this->operators = array(
            '>' => '大于',
            '<' => '小于',
            '=' => '等于',
            '>=' => '大于等于',
            '<=' => '小于等于',
            '!=' => '不等于'
        );
    }

    /**
     * 升贴水规则列表
     */
    public function index(){
        $page = I('get.page', 1);
        $pageSize = 20;
        
        $model = M('PremiumDiscountRule');
        $count = $model->count();
        
        $rules = $model->order('id desc')
                      ->page($page, $pageSize)
                      ->select();
        
        // 获取每个规则的详情数量
        foreach($rules as &$rule) {
            $rule['detail_count'] = M('PremiumDiscountRuleDetail')
                                  ->where(array('rule_id' => $rule['id']))
                                  ->count();
        }
        
        $this->assign('rules', $rules);
        $this->assign('count', $count);
        $this->assign('page', $page);
        $this->assign('pageSize', $pageSize);
        $this->display();
    }

    /**
     * 添加规则页面
     */
    public function add(){
        $this->display();
    }

    /**
     * 执行添加规则
     */
    public function runAdd(){
        $model = M('PremiumDiscountRule');
        
        $data = array(
            'rule_name' => I('post.rule_name'),
            'is_enabled' => I('post.is_enabled', 1),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        // 验证规则名称是否重复
        $exists = $model->where(array('rule_name' => $data['rule_name']))->find();
        if($exists) {
            die(json_encode(array('code' => 0, 'msg' => '规则名称已存在')));
        }
        
        if($model->add($data)) {
            die(json_encode(array('code' => 1, 'msg' => '添加成功')));
        } else {
            die(json_encode(array('code' => 0, 'msg' => '添加失败')));
        }
    }

    /**
     * 编辑规则页面
     */
    public function edit(){
        $id = I('get.id');
        $rule = M('PremiumDiscountRule')->find($id);
        
        if(!$rule) {
            $this->error('规则不存在');
        }
        
        $this->assign('rule', $rule);
        $this->display();
    }

    /**
     * 执行编辑规则
     */
    public function runEdit(){
        $model = M('PremiumDiscountRule');
        $id = I('post.id');
        
        $data = array(
            'id' => $id,
            'rule_name' => I('post.rule_name'),
            'is_enabled' => I('post.is_enabled', 1),
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        // 验证规则名称是否重复（排除当前记录）
        $exists = $model->where(array('rule_name' => $data['rule_name'], 'id' => array('neq', $id)))->find();
        if($exists) {
            die(json_encode(array('code' => 0, 'msg' => '规则名称已存在')));
        }
        
        if($model->save($data)) {
            die(json_encode(array('code' => 1, 'msg' => '修改成功')));
        } else {
            die(json_encode(array('code' => 0, 'msg' => '修改失败')));
        }
    }

    /**
     * 删除规则
     */
    public function del(){
        $id = I('get.id');
        
        // 检查是否有关联的规则详情
        $detailCount = M('PremiumDiscountRuleDetail')->where(array('rule_id' => $id))->count();
        if($detailCount > 0) {
            die(json_encode(array('code' => 0, 'msg' => '请先删除该规则下的所有详情配置')));
        }
        
        if(M('PremiumDiscountRule')->delete($id)) {
            die(json_encode(array('code' => 1, 'msg' => '删除成功')));
        } else {
            die(json_encode(array('code' => 0, 'msg' => '删除失败')));
        }
    }

    /**
     * 切换规则启用状态
     */
    public function toggleStatus(){
        $id = I('get.id');
        $rule = M('PremiumDiscountRule')->find($id);
        
        if(!$rule) {
            die(json_encode(array('code' => 0, 'msg' => '规则不存在')));
        }
        
        $newStatus = $rule['is_enabled'] ? 0 : 1;
        $data = array(
            'id' => $id,
            'is_enabled' => $newStatus,
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        if(M('PremiumDiscountRule')->save($data)) {
            $statusText = $newStatus ? '启用' : '禁用';
            die(json_encode(array('code' => 1, 'msg' => $statusText . '成功')));
        } else {
            die(json_encode(array('code' => 0, 'msg' => '操作失败')));
        }
    }

    /**
     * 规则详情管理
     */
    public function detail(){
        $ruleId = I('get.rule_id');
        $rule = M('PremiumDiscountRule')->find($ruleId);
        
        if(!$rule) {
            $this->error('规则不存在');
        }
        
        $details = M('PremiumDiscountRuleDetail')
                  ->where(array('rule_id' => $ruleId))
                  ->order('id desc')
                  ->select();
        
        $this->assign('rule', $rule);
        $this->assign('details', $details);
        $this->assign('condition_fields', $this->condition_fields);
        $this->assign('operators', $this->operators);
        $this->display();
    }

    /**
     * 添加规则详情
     */
    public function addDetail(){
        $ruleId = I('get.rule_id');
        $rule = M('PremiumDiscountRule')->find($ruleId);
        
        if(!$rule) {
            $this->error('规则不存在');
        }
        
        $this->assign('rule', $rule);
        $this->assign('condition_fields', $this->condition_fields);
        $this->assign('operators', $this->operators);
        $this->display();
    }

    /**
     * 执行添加规则详情
     */
    public function runAddDetail(){
        $model = M('PremiumDiscountRuleDetail');
        
        $data = array(
            'rule_id' => I('post.rule_id'),
            'condition_field' => I('post.condition_field'),
            'operator' => I('post.operator'),
            'condition_value' => I('post.condition_value'),
            'discount_value' => I('post.discount_value'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        if($model->add($data)) {
            die(json_encode(array('code' => 1, 'msg' => '添加成功')));
        } else {
            die(json_encode(array('code' => 0, 'msg' => '添加失败')));
        }
    }

    /**
     * 编辑规则详情
     */
    public function editDetail(){
        $id = I('get.id');
        $detail = M('PremiumDiscountRuleDetail')->find($id);
        
        if(!$detail) {
            $this->error('详情不存在');
        }
        
        $rule = M('PremiumDiscountRule')->find($detail['rule_id']);
        
        $this->assign('detail', $detail);
        $this->assign('rule', $rule);
        $this->assign('condition_fields', $this->condition_fields);
        $this->assign('operators', $this->operators);
        $this->display();
    }

    /**
     * 执行编辑规则详情
     */
    public function runEditDetail(){
        $model = M('PremiumDiscountRuleDetail');
        
        $data = array(
            'id' => I('post.id'),
            'condition_field' => I('post.condition_field'),
            'operator' => I('post.operator'),
            'condition_value' => I('post.condition_value'),
            'discount_value' => I('post.discount_value'),
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        if($model->save($data)) {
            die(json_encode(array('code' => 1, 'msg' => '修改成功')));
        } else {
            die(json_encode(array('code' => 0, 'msg' => '修改失败')));
        }
    }

    /**
     * 删除规则详情
     */
    public function delDetail(){
        $id = I('get.id');
        
        if(M('PremiumDiscountRuleDetail')->delete($id)) {
            die(json_encode(array('code' => 1, 'msg' => '删除成功')));
        } else {
            die(json_encode(array('code' => 0, 'msg' => '删除失败')));
        }
    }
}
