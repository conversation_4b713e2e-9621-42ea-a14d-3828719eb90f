<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">

<title>后台登录</title>

<meta name="renderer" content="webkit|ie-comp|ie-stand">

<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

<meta http-equiv="Cache-Control" content="no-siteapp" />

<link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

<link rel="stylesheet" href="/Public/xadmin/css/font.css">

<link rel="stylesheet" href="/Public/xadmin/css/xadmin.css">

<script type="text/javascript" src="/Public/xadmin/js/jquery.min.js"></script>

<script src="/Public/xadmin/lib/layui/layui.js" charset="utf-8"></script>

<script type="text/javascript" src="/Public/xadmin/js/xadmin.js"></script>

<link rel="stylesheet" href="/Public/css/back2.css">

<link rel="stylesheet" href="/Public/font-awesome/css/font-awesome.min.css">


</head>
<body>
    <div class="x-body">
        <div class="layui-card">
            <div class="layui-card-header">
                <h3>规则详情管理 - <?php echo ($rule["rule_name"]); ?></h3>
                <p>规则ID：<?php echo ($rule["id"]); ?> | 状态：
                    <?php if($rule['is_enabled'] == 1): ?><span class="layui-badge layui-bg-green">启用</span>
                    <?php else: ?>
                        <span class="layui-badge">禁用</span><?php endif; ?>
                </p>
            </div>
        </div>
        
        <button id="add_detail_click" class="layui-btn"><i class="layui-icon"></i>添加详情</button>
        <a href="<?php echo U('index');?>" class="layui-btn layui-btn-primary">返回规则列表</a>
        
        <script>
            $('#add_detail_click').click(function(){
                var aa = "<?php echo U('addDetail', array('rule_id'=>$rule['id']));?>";
                x_admin_show('添加规则详情', aa);
            });
        </script>

        <table class="tbl2 bg_f w_100 rad_10 mt_10 clr_79">
            <thead>
                <tr>
                    <th class="al_lt">条件字段</th>
                    <th class="al_lt">操作符</th>
                    <th class="al_lt">条件值</th>
                    <th class="al_lt">升贴水值</th>
                    <th class="al_lt">创建时间</th>
                    <th class="al_lt">操作</th>
                </tr>
            </thead>
            <tbody>
                <?php if(empty($details)): ?><tr>
                        <td colspan="6" style="text-align: center; padding: 20px;">
                            暂无规则详情，请添加详情配置
                        </td>
                    </tr>
                <?php else: ?>
                    <?php if(is_array($details)): foreach($details as $key=>$v): ?><tr>
                            <td>
                                <?php echo ($condition_fields[$v['condition_field']]); ?>
                                <span style="color: #999;">(<?php echo ($v["condition_field"]); ?>)</span>
                            </td>
                            <td><?php echo ($operators[$v['operator']]); ?></td>
                            <td><?php echo ($v["condition_value"]); ?></td>
                            <td>
                                <?php if($v['discount_value'] > 0): ?><span style="color: #5FB878;">+<?php echo ($v["discount_value"]); ?></span>
                                <?php elseif($v['discount_value'] < 0): ?>
                                    <span style="color: #FF5722;"><?php echo ($v["discount_value"]); ?></span>
                                <?php else: ?>
                                    <span><?php echo ($v["discount_value"]); ?></span><?php endif; ?>
                            </td>
                            <td><?php echo ($v["created_at"]); ?></td>
                            <td>
                                <a href="javascript:;" my_url="<?php echo U('editDetail',array('id'=>$v['id']));?>" class="edit_detail_item layui-btn layui-btn-xs" title="编辑">编辑</a>
                                <a href="javascript:;" my_url="<?php echo U('delDetail', array('id'=>$v['id']));?>" class="del_detail_item layui-btn layui-btn-xs layui-btn-danger" title="删除">删除</a>
                            </td>
                        </tr><?php endforeach; endif; endif; ?>
            </tbody>
        </table>

        <script>
            $('.edit_detail_item').click(function(){
                x_admin_show('编辑规则详情', $(this).attr('my_url'));
            });
            
            $('.del_detail_item').click(function(){
                var aa = $(this);
                layer.confirm('确认删除？删除后不可恢复！', {icon: 3, title:'提示'}, function(index){
                    $.getJSON(aa.attr('my_url'), function(d){
                        if(d.code==1){
                            layer.msg(d.msg, {icon: 1});
                            setTimeout(function(){
                                history.go(0);
                            }, 1000);
                        }else{
                            layer.msg(d.msg, {icon: 2});
                        }
                    });
                    layer.close(index);
                });
            });
        </script>
    </div>
</body>
</html>