<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">

<title>后台登录</title>

<meta name="renderer" content="webkit|ie-comp|ie-stand">

<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

<meta http-equiv="Cache-Control" content="no-siteapp" />

<link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

<link rel="stylesheet" href="/Public/xadmin/css/font.css">

<link rel="stylesheet" href="/Public/xadmin/css/xadmin.css">

<script type="text/javascript" src="/Public/xadmin/js/jquery.min.js"></script>

<script src="/Public/xadmin/lib/layui/layui.js" charset="utf-8"></script>

<script type="text/javascript" src="/Public/xadmin/js/xadmin.js"></script>

<link rel="stylesheet" href="/Public/css/back2.css">

<link rel="stylesheet" href="/Public/font-awesome/css/font-awesome.min.css">


</head>
<body>
    <div class="x-body">
        <div class="layui-card">
            <div class="layui-card-header">
                <h4>添加规则详情 - <?php echo ($rule["rule_name"]); ?></h4>
            </div>
        </div>
        
        <form class="layui-form" id="form1">
            <input type="hidden" name="rule_id" value="<?php echo ($rule["id"]); ?>">
            
            <div class="layui-form-item">
                <label for="condition_field" class="layui-form-label">
                    <span class="x-red">*</span>条件字段
                </label>
                <div class="layui-input-inline">
                    <select name="condition_field" lay-verify="required">
                        <option value="">请选择条件字段</option>
                        <?php if(is_array($condition_fields)): foreach($condition_fields as $k=>$v): ?><option value="<?php echo ($k); ?>"><?php echo ($v); ?></option><?php endforeach; endif; ?>
                    </select>
                </div>
                <div class="layui-form-mid layui-word-aux">
                    选择要设置条件的字段
                </div>
            </div>
            
            <div class="layui-form-item">
                <label for="operator" class="layui-form-label">
                    <span class="x-red">*</span>操作符
                </label>
                <div class="layui-input-inline">
                    <select name="operator" lay-verify="required">
                        <option value="">请选择操作符</option>
                        <?php if(is_array($operators)): foreach($operators as $k=>$v): ?><option value="<?php echo ($k); ?>"><?php echo ($v); ?></option><?php endforeach; endif; ?>
                    </select>
                </div>
                <div class="layui-form-mid layui-word-aux">
                    选择比较操作符
                </div>
            </div>
            
            <div class="layui-form-item">
                <label for="condition_value" class="layui-form-label">
                    <span class="x-red">*</span>条件值
                </label>
                <div class="layui-input-inline">
                    <input type="text" id="condition_value" name="condition_value" required="" lay-verify="required"
                           autocomplete="off" class="layui-input" placeholder="请输入条件值">
                </div>
                <div class="layui-form-mid layui-word-aux">
                    <span class="x-red">*</span>用于比较的值
                </div>
            </div>
            
            <div class="layui-form-item">
                <label for="discount_value" class="layui-form-label">
                    <span class="x-red">*</span>升贴水值
                </label>
                <div class="layui-input-inline">
                    <input type="number" step="0.01" id="discount_value" name="discount_value" required="" lay-verify="required"
                           autocomplete="off" class="layui-input" placeholder="请输入升贴水值">
                </div>
                <div class="layui-form-mid layui-word-aux">
                    <span class="x-red">*</span>正数为升水，负数为贴水
                </div>
            </div>
            
            <div class="layui-form-item">
                <label for="" class="layui-form-label"></label>
                <button class="layui-btn" lay-filter="add" lay-submit="">增加</button>
            </div>
        </form>
    </div>
    
    <script>
        layui.use(['form','layer'], function(){
            $ = layui.jquery;
            var form = layui.form,
                layer = layui.layer;

            //自定义验证规则
            form.verify({
                condition_value: function(value){
                    if(value.length < 1){
                        return '条件值不能为空';
                    }
                },
                discount_value: function(value){
                    if(isNaN(value)){
                        return '升贴水值必须是数字';
                    }
                }
            });

            //监听提交
            form.on('submit(add)', function(data){
                console.log(data);
                //发异步，把数据提交给php
                $.ajax({
                    url: "<?php echo U('runAddDetail');?>",
                    type: 'POST',
                    data: data.field,
                    dataType: 'json',
                    success: function(res){
                        if(res.code == 1){
                            layer.alert("增加成功", {icon: 1}, function(){
                                // 获得frame索引
                                var index = parent.layer.getFrameIndex(window.name);
                                //关闭当前frame
                                parent.layer.close(index);
                                // 刷新父页面
                                parent.location.reload();
                            });
                        }else{
                            layer.alert(res.msg, {icon: 2});
                        }
                    },
                    error: function(){
                        layer.alert("请求失败", {icon: 2});
                    }
                });
                return false;
            });
        });
    </script>
</body>
</html>